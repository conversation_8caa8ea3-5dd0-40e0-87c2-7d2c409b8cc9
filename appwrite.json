{"projectId": "gemini-ai-function", "projectName": "Gemini AI Function", "functions": [{"functionId": "gemini-ai-chat", "name": "Gemini AI Chat", "runtime": "python-3.12", "source": "./src", "entrypoint": "main.py", "commands": "pip install -r requirements.txt", "ignore": ["node_modules", ".git", ".appwrite", "__pycache__", "*.pyc", ".env", ".venv", "venv"], "execute": ["any"], "events": [], "schedule": "", "timeout": 30, "enabled": true, "logging": true, "variables": {"GEMINI_API_KEY": ""}}]}