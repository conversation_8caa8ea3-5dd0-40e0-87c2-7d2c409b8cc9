#!/bin/bash

# Example cURL requests for testing the Gemini AI Appwrite Function
# Replace the placeholders with your actual values

# Configuration
APPWRITE_ENDPOINT="https://cloud.appwrite.io/v1"  # Replace with your Appwrite endpoint
PROJECT_ID="your-project-id"                      # Replace with your project ID
FUNCTION_ID="gemini-ai-chat"                      # Function ID from appwrite.json

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🤖 Gemini AI Appwrite Function - cURL Examples${NC}"
echo "=================================================="

# Check if required variables are set
if [ "$PROJECT_ID" = "your-project-id" ]; then
    echo -e "${RED}❌ Please update PROJECT_ID in this script${NC}"
    exit 1
fi

echo -e "${YELLOW}📝 Configuration:${NC}"
echo "Endpoint: $APPWRITE_ENDPOINT"
echo "Project ID: $PROJECT_ID"
echo "Function ID: $FUNCTION_ID"
echo ""

# Example 1: Simple question
echo -e "${BLUE}Example 1: Simple Question${NC}"
echo "Request: What is artificial intelligence?"
echo ""

curl -X POST \
  "$APPWRITE_ENDPOINT/functions/$FUNCTION_ID/executions" \
  -H "Content-Type: application/json" \
  -H "X-Appwrite-Project: $PROJECT_ID" \
  -d '{
    "prompt": "What is artificial intelligence? Explain it in simple terms."
  }' | jq '.'

echo -e "\n${GREEN}✅ Example 1 completed${NC}\n"

# Example 2: Technical question
echo -e "${BLUE}Example 2: Technical Question${NC}"
echo "Request: Explain quantum computing"
echo ""

curl -X POST \
  "$APPWRITE_ENDPOINT/functions/$FUNCTION_ID/executions" \
  -H "Content-Type: application/json" \
  -H "X-Appwrite-Project: $PROJECT_ID" \
  -d '{
    "prompt": "Explain quantum computing and its potential applications in detail."
  }' | jq '.'

echo -e "\n${GREEN}✅ Example 2 completed${NC}\n"

# Example 3: Creative writing
echo -e "${BLUE}Example 3: Creative Writing${NC}"
echo "Request: Write a short story"
echo ""

curl -X POST \
  "$APPWRITE_ENDPOINT/functions/$FUNCTION_ID/executions" \
  -H "Content-Type: application/json" \
  -H "X-Appwrite-Project: $PROJECT_ID" \
  -d '{
    "prompt": "Write a short science fiction story about a robot discovering emotions."
  }' | jq '.'

echo -e "\n${GREEN}✅ Example 3 completed${NC}\n"

# Example 4: Code generation
echo -e "${BLUE}Example 4: Code Generation${NC}"
echo "Request: Generate Python code"
echo ""

curl -X POST \
  "$APPWRITE_ENDPOINT/functions/$FUNCTION_ID/executions" \
  -H "Content-Type: application/json" \
  -H "X-Appwrite-Project: $PROJECT_ID" \
  -d '{
    "prompt": "Write a Python function that calculates the factorial of a number using recursion."
  }' | jq '.'

echo -e "\n${GREEN}✅ Example 4 completed${NC}\n"

# Example 5: Error case - missing prompt
echo -e "${BLUE}Example 5: Error Case - Missing Prompt${NC}"
echo "Request: Empty payload (should return error)"
echo ""

curl -X POST \
  "$APPWRITE_ENDPOINT/functions/$FUNCTION_ID/executions" \
  -H "Content-Type: application/json" \
  -H "X-Appwrite-Project: $PROJECT_ID" \
  -d '{}' | jq '.'

echo -e "\n${RED}❌ Example 5 completed (expected error)${NC}\n"

echo -e "${GREEN}🎉 All examples completed!${NC}"
echo ""
echo -e "${YELLOW}💡 Tips:${NC}"
echo "- Make sure your GEMINI_API_KEY is set in the function environment"
echo "- Check function logs in Appwrite console for debugging"
echo "- Modify the prompts above to test different scenarios"
echo "- Use jq to format JSON responses (install with: apt-get install jq)"
