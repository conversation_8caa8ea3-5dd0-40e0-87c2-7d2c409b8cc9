/**
 * JavaScript example for using the Gemini AI Appwrite Function
 * This example shows how to integrate the function in a web application
 */

import { Client, Functions } from 'appwrite';

// Configuration
const APPWRITE_ENDPOINT = 'https://cloud.appwrite.io/v1'; // Replace with your endpoint
const PROJECT_ID = 'your-project-id'; // Replace with your project ID
const FUNCTION_ID = 'gemini-ai-chat';

// Initialize Appwrite client
const client = new Client()
    .setEndpoint(APPWRITE_ENDPOINT)
    .setProject(PROJECT_ID);

const functions = new Functions(client);

/**
 * Call the Gemini AI function with a prompt
 * @param {string} prompt - The prompt to send to Gemini AI
 * @returns {Promise<string>} - The AI response
 */
async function callGeminiAI(prompt) {
    try {
        console.log('🤖 Sending prompt to Gemini AI:', prompt);
        
        const execution = await functions.createExecution(
            FUNCTION_ID,
            JSON.stringify({ prompt })
        );
        
        // Parse the response
        const response = JSON.parse(execution.response);
        
        if (execution.status === 'completed' && response.response) {
            console.log('✅ Success:', response.response);
            return response.response;
        } else {
            console.error('❌ Error:', response.error || 'Unknown error');
            throw new Error(response.error || 'Function execution failed');
        }
        
    } catch (error) {
        console.error('❌ Failed to call Gemini AI:', error);
        throw error;
    }
}

/**
 * Example usage in a chat interface
 */
class GeminiChatInterface {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.setupUI();
    }
    
    setupUI() {
        this.container.innerHTML = `
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages"></div>
                <div class="chat-input">
                    <input type="text" id="prompt-input" placeholder="Ask Gemini AI anything..." />
                    <button id="send-button">Send</button>
                </div>
            </div>
        `;
        
        // Add event listeners
        const sendButton = document.getElementById('send-button');
        const promptInput = document.getElementById('prompt-input');
        
        sendButton.addEventListener('click', () => this.sendMessage());
        promptInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });
    }
    
    async sendMessage() {
        const promptInput = document.getElementById('prompt-input');
        const prompt = promptInput.value.trim();
        
        if (!prompt) return;
        
        // Clear input
        promptInput.value = '';
        
        // Add user message to chat
        this.addMessage('user', prompt);
        
        // Show loading
        const loadingId = this.addMessage('ai', 'Thinking...', true);
        
        try {
            // Call Gemini AI
            const response = await callGeminiAI(prompt);
            
            // Replace loading message with response
            this.updateMessage(loadingId, response);
            
        } catch (error) {
            this.updateMessage(loadingId, `Error: ${error.message}`);
        }
    }
    
    addMessage(sender, message, isLoading = false) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageId = `msg-${Date.now()}`;
        
        const messageElement = document.createElement('div');
        messageElement.id = messageId;
        messageElement.className = `message ${sender}-message ${isLoading ? 'loading' : ''}`;
        messageElement.innerHTML = `
            <div class="message-content">${message}</div>
            <div class="message-time">${new Date().toLocaleTimeString()}</div>
        `;
        
        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        return messageId;
    }
    
    updateMessage(messageId, newContent) {
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
            const contentElement = messageElement.querySelector('.message-content');
            contentElement.textContent = newContent;
            messageElement.classList.remove('loading');
        }
    }
}

/**
 * Example functions for different use cases
 */

// Example 1: Simple Q&A
async function askQuestion() {
    try {
        const response = await callGeminiAI("What are the benefits of renewable energy?");
        console.log("Answer:", response);
    } catch (error) {
        console.error("Failed to get answer:", error);
    }
}

// Example 2: Code generation
async function generateCode() {
    const prompt = `
        Write a JavaScript function that:
        1. Takes an array of numbers as input
        2. Returns the sum of all even numbers in the array
        3. Includes proper error handling
    `;
    
    try {
        const code = await callGeminiAI(prompt);
        console.log("Generated code:", code);
    } catch (error) {
        console.error("Failed to generate code:", error);
    }
}

// Example 3: Creative writing
async function writeStory() {
    const prompt = "Write a short story about a time traveler who accidentally changes history.";
    
    try {
        const story = await callGeminiAI(prompt);
        console.log("Generated story:", story);
    } catch (error) {
        console.error("Failed to write story:", error);
    }
}

// Example 4: Data analysis
async function analyzeData() {
    const prompt = `
        Analyze this sales data and provide insights:
        Q1: $50,000
        Q2: $75,000
        Q3: $60,000
        Q4: $90,000
        
        What trends do you see and what recommendations would you make?
    `;
    
    try {
        const analysis = await callGeminiAI(prompt);
        console.log("Data analysis:", analysis);
    } catch (error) {
        console.error("Failed to analyze data:", error);
    }
}

// Example 5: Batch processing
async function batchProcess() {
    const prompts = [
        "Explain machine learning in one sentence",
        "What is the capital of France?",
        "Write a haiku about programming"
    ];
    
    console.log("Processing multiple prompts...");
    
    for (let i = 0; i < prompts.length; i++) {
        try {
            console.log(`\nPrompt ${i + 1}: ${prompts[i]}`);
            const response = await callGeminiAI(prompts[i]);
            console.log(`Response ${i + 1}: ${response}`);
        } catch (error) {
            console.error(`Failed prompt ${i + 1}:`, error);
        }
    }
}

// Export functions for use in other modules
export {
    callGeminiAI,
    GeminiChatInterface,
    askQuestion,
    generateCode,
    writeStory,
    analyzeData,
    batchProcess
};

// Example usage when script is run directly
if (typeof window !== 'undefined') {
    // Browser environment
    window.GeminiAI = {
        callGeminiAI,
        GeminiChatInterface,
        askQuestion,
        generateCode,
        writeStory,
        analyzeData,
        batchProcess
    };
    
    console.log('🤖 Gemini AI JavaScript examples loaded!');
    console.log('Available functions:', Object.keys(window.GeminiAI));
}
