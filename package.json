{"name": "gemini-ai-appwrite-function", "version": "1.0.0", "description": "Appwrite function that integrates Google Gemini AI for intelligent text generation", "main": "src/main.py", "scripts": {"deploy": "appwrite deploy function", "logs": "appwrite functions listExecutions --functionId=gemini-ai-chat", "test": "appwrite functions createExecution --functionId=gemini-ai-chat --data='{\"prompt\":\"Hello, world!\"}'", "setup": "pip install -r requirements.txt"}, "keywords": ["appwrite", "function", "gemini", "ai", "google", "serverless", "python"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/gemini-ai-appwrite.git"}, "bugs": {"url": "https://github.com/yourusername/gemini-ai-appwrite/issues"}, "homepage": "https://github.com/yourusername/gemini-ai-appwrite#readme", "devDependencies": {"appwrite-cli": "^4.0.0"}}