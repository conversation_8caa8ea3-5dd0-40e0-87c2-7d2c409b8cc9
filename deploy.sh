#!/bin/bash

# Deployment script for Gemini AI Appwrite Function
# This script automates the deployment process

echo "🚀 Starting deployment of Gemini AI Appwrite Function..."

# Check if Appwrite CLI is installed
if ! command -v appwrite &> /dev/null; then
    echo "❌ Appwrite CLI is not installed. Please install it first:"
    echo "npm install -g appwrite-cli"
    exit 1
fi

# Check if user is logged in
if ! appwrite account get &> /dev/null; then
    echo "❌ You are not logged in to Appwrite. Please login first:"
    echo "appwrite login"
    exit 1
fi

# Validate required files
echo "📋 Checking required files..."
required_files=("src/main.py" "appwrite.json" "requirements.txt")

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Required file missing: $file"
        exit 1
    fi
done

echo "✅ All required files found"

# Deploy the function
echo "📦 Deploying function..."
if appwrite deploy function; then
    echo "✅ Function deployed successfully!"
    echo ""
    echo "📝 Next steps:"
    echo "1. Set your GEMINI_API_KEY environment variable in the Appwrite console"
    echo "2. Test the function with a sample request"
    echo "3. Check the function logs for any issues"
    echo ""
    echo "🔗 Function URL will be available in your Appwrite console"
else
    echo "❌ Deployment failed. Please check the error messages above."
    exit 1
fi
