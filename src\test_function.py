"""
Test script for the Gemini AI Appwrite function.
This script helps test the function locally before deployment.
"""

import json
import os
from main import main

class MockRequest:
    """Mock request object for testing"""
    def __init__(self, payload, variables=None):
        self.payload = payload
        self.variables = variables or {}

class MockResponse:
    """Mock response object for testing"""
    def __init__(self):
        self.status_code = 200
        self.response_data = None
    
    def json(self, data, status_code=200):
        self.response_data = data
        self.status_code = status_code
        return {
            "data": data,
            "status": status_code
        }

def test_function():
    """Test the main function with sample data"""
    
    # Test 1: Valid request
    print("Test 1: Valid request")
    payload = json.dumps({"prompt": "What is artificial intelligence?"})
    variables = {"GEMINI_API_KEY": os.getenv("GEMINI_API_KEY", "")}
    
    req = MockRequest(payload, variables)
    res = MockResponse()
    
    result = main(req, res)
    print(f"Status: {res.status_code}")
    print(f"Response: {result}")
    print("-" * 50)
    
    # Test 2: Missing API key
    print("Test 2: Missing API key")
    req = MockRequest(payload, {})
    res = MockResponse()
    
    result = main(req, res)
    print(f"Status: {res.status_code}")
    print(f"Response: {result}")
    print("-" * 50)
    
    # Test 3: Invalid JSON payload
    print("Test 3: Invalid JSON payload")
    req = MockRequest("invalid json", variables)
    res = MockResponse()
    
    result = main(req, res)
    print(f"Status: {res.status_code}")
    print(f"Response: {result}")
    print("-" * 50)
    
    # Test 4: Missing prompt
    print("Test 4: Missing prompt")
    payload = json.dumps({"message": "This is not a prompt"})
    req = MockRequest(payload, variables)
    res = MockResponse()
    
    result = main(req, res)
    print(f"Status: {res.status_code}")
    print(f"Response: {result}")
    print("-" * 50)

if __name__ == "__main__":
    # Check if API key is set
    if not os.getenv("GEMINI_API_KEY"):
        print("Warning: GEMINI_API_KEY environment variable not set.")
        print("Set it with: export GEMINI_API_KEY='your-api-key'")
        print("Some tests will fail without it.")
        print()
    
    test_function()
