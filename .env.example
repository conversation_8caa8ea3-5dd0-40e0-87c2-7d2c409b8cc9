# Environment Variables for Gemini AI Appwrite Function
# Copy this file to .env and fill in your actual values

# Google AI Studio API Key
# Get this from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Appwrite Configuration (for local testing)
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=your_project_id_here
APPWRITE_FUNCTION_ID=gemini-ai-chat

# Optional: Custom model configuration
GEMINI_MODEL=gemini-2.5-pro
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=1000

# Development settings
DEBUG=false
LOG_LEVEL=info
