import os
import json
import google.generativeai as genai

# This is the main entry point for the Appwrite function.
# req: The request object, containing headers, payload, and variables.
# res: The response object, used to send a response back to the client.
def main(req, res):
    # Get the Gemini API Key from Appwrite's environment variables.
    api_key = req.variables.get("GEMINI_API_KEY")

    if not api_key:
        # If the API key is not set, return a 500 error.
        return res.json({
            "error": "GEMINI_API_KEY environment variable not set."
        }, 500)

    # Configure the Gemini client.
    genai.configure(api_key=api_key)

    # Validate the request payload.
    try:
        payload = json.loads(req.payload)
        prompt = payload.get("prompt")
        if not prompt:
            raise ValueError("Missing 'prompt' in request payload.")
    except (json.JSONDecodeError, ValueError) as e:
        # If payload is invalid or prompt is missing, return a 400 error.
        return res.json({"error": str(e)}, 400)

    try:
        # Initialize the Gemini Pro model.
        # Using "gemini-1.5-pro-latest" for access to the latest features.
        model = genai.GenerativeModel("gemini-2.5-pro")

        # Asynchronously generate content from the prompt.
        response = model.generate_content(
            prompt,
            # Enable Google Search as a tool for fact-checking and real-time info.
            tools=['Google Search'],
        )
        
        # Return the generated text in a JSON response.
        return res.json({
            "response": response.text
        })

    except Exception as e:
        # Catch any exceptions from the Gemini API call.
        return res.json({
            "error": f"An error occurred while calling the Gemini API: {str(e)}"
        }, 500)