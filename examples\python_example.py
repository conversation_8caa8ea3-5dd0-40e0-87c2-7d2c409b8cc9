"""
Python example for using the Gemini AI Appwrite Function
This example shows how to integrate the function in a Python application
"""

import json
import asyncio
from typing import Optional, List, Dict, Any
from appwrite.client import Client
from appwrite.services.functions import Functions
from appwrite.exception import AppwriteException

# Configuration
APPWRITE_ENDPOINT = "https://cloud.appwrite.io/v1"  # Replace with your endpoint
PROJECT_ID = "your-project-id"  # Replace with your project ID
FUNCTION_ID = "gemini-ai-chat"

class GeminiAIClient:
    """Client for interacting with the Gemini AI Appwrite Function"""
    
    def __init__(self, endpoint: str = APPWRITE_ENDPOINT, project_id: str = PROJECT_ID):
        """Initialize the client"""
        self.client = Client()
        self.client.set_endpoint(endpoint)
        self.client.set_project(project_id)
        self.functions = Functions(self.client)
    
    async def ask(self, prompt: str) -> str:
        """
        Send a prompt to Gemini AI and get the response
        
        Args:
            prompt (str): The prompt to send to Gemini AI
            
        Returns:
            str: The AI response
            
        Raises:
            Exception: If the function call fails
        """
        try:
            print(f"🤖 Sending prompt to Gemini AI: {prompt[:100]}...")
            
            # Create function execution
            execution = self.functions.create_execution(
                function_id=FUNCTION_ID,
                data=json.dumps({"prompt": prompt})
            )
            
            # Parse response
            response_data = json.loads(execution['response'])
            
            if execution['status'] == 'completed' and 'response' in response_data:
                print("✅ Success!")
                return response_data['response']
            else:
                error_msg = response_data.get('error', 'Unknown error')
                print(f"❌ Error: {error_msg}")
                raise Exception(error_msg)
                
        except AppwriteException as e:
            print(f"❌ Appwrite error: {e}")
            raise
        except Exception as e:
            print(f"❌ Failed to call Gemini AI: {e}")
            raise

class GeminiChatBot:
    """Interactive chatbot using Gemini AI"""
    
    def __init__(self):
        self.client = GeminiAIClient()
        self.conversation_history = []
    
    async def chat(self):
        """Start an interactive chat session"""
        print("🤖 Gemini AI Chatbot")
        print("Type 'quit' to exit, 'history' to see conversation history")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\nYou: ").strip()
                
                if user_input.lower() == 'quit':
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'history':
                    self.show_history()
                    continue
                elif not user_input:
                    continue
                
                # Get AI response
                response = await self.client.ask(user_input)
                print(f"\nGemini: {response}")
                
                # Save to history
                self.conversation_history.append({
                    "user": user_input,
                    "ai": response
                })
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
    
    def show_history(self):
        """Display conversation history"""
        if not self.conversation_history:
            print("No conversation history yet.")
            return
        
        print("\n📜 Conversation History:")
        print("-" * 30)
        for i, exchange in enumerate(self.conversation_history, 1):
            print(f"{i}. You: {exchange['user']}")
            print(f"   AI: {exchange['ai'][:100]}...")
            print()

# Example functions for different use cases

async def example_qa():
    """Example: Question and Answer"""
    client = GeminiAIClient()
    
    questions = [
        "What is artificial intelligence?",
        "How does machine learning work?",
        "What are the benefits of renewable energy?",
        "Explain quantum computing in simple terms."
    ]
    
    print("🔍 Q&A Examples")
    print("=" * 50)
    
    for question in questions:
        try:
            answer = await client.ask(question)
            print(f"\nQ: {question}")
            print(f"A: {answer[:200]}...")
        except Exception as e:
            print(f"❌ Failed to get answer for '{question}': {e}")

async def example_code_generation():
    """Example: Code Generation"""
    client = GeminiAIClient()
    
    prompts = [
        "Write a Python function to calculate fibonacci numbers",
        "Create a JavaScript function to validate email addresses",
        "Write a SQL query to find the top 5 customers by sales",
        "Generate a Python class for a simple bank account"
    ]
    
    print("💻 Code Generation Examples")
    print("=" * 50)
    
    for prompt in prompts:
        try:
            code = await client.ask(prompt)
            print(f"\nPrompt: {prompt}")
            print(f"Generated Code:\n{code[:300]}...")
            print("-" * 30)
        except Exception as e:
            print(f"❌ Failed to generate code for '{prompt}': {e}")

async def example_creative_writing():
    """Example: Creative Writing"""
    client = GeminiAIClient()
    
    prompts = [
        "Write a short poem about technology",
        "Create a story about a robot learning to paint",
        "Write a product description for a smart home device",
        "Compose a professional email declining a job offer"
    ]
    
    print("✍️ Creative Writing Examples")
    print("=" * 50)
    
    for prompt in prompts:
        try:
            content = await client.ask(prompt)
            print(f"\nPrompt: {prompt}")
            print(f"Generated Content:\n{content[:250]}...")
            print("-" * 30)
        except Exception as e:
            print(f"❌ Failed to generate content for '{prompt}': {e}")

async def example_data_analysis():
    """Example: Data Analysis"""
    client = GeminiAIClient()
    
    data_prompt = """
    Analyze this sales data and provide insights:
    
    Monthly Sales (2023):
    Jan: $45,000
    Feb: $52,000
    Mar: $48,000
    Apr: $61,000
    May: $58,000
    Jun: $67,000
    Jul: $72,000
    Aug: $69,000
    Sep: $74,000
    Oct: $78,000
    Nov: $82,000
    Dec: $89,000
    
    Please provide:
    1. Trend analysis
    2. Key insights
    3. Recommendations for next year
    """
    
    print("📊 Data Analysis Example")
    print("=" * 50)
    
    try:
        analysis = await client.ask(data_prompt)
        print(f"Analysis:\n{analysis}")
    except Exception as e:
        print(f"❌ Failed to analyze data: {e}")

async def example_batch_processing():
    """Example: Batch Processing"""
    client = GeminiAIClient()
    
    tasks = [
        {"type": "summary", "content": "Summarize the benefits of cloud computing"},
        {"type": "translation", "content": "Translate 'Hello, how are you?' to Spanish"},
        {"type": "explanation", "content": "Explain blockchain technology"},
        {"type": "recommendation", "content": "Recommend 3 books about Python programming"}
    ]
    
    print("🔄 Batch Processing Example")
    print("=" * 50)
    
    results = []
    for i, task in enumerate(tasks, 1):
        try:
            print(f"\nProcessing task {i}/{len(tasks)}: {task['type']}")
            response = await client.ask(task['content'])
            results.append({
                "task": task,
                "response": response,
                "status": "success"
            })
            print(f"✅ Completed: {response[:100]}...")
        except Exception as e:
            results.append({
                "task": task,
                "error": str(e),
                "status": "failed"
            })
            print(f"❌ Failed: {e}")
    
    # Summary
    successful = len([r for r in results if r['status'] == 'success'])
    print(f"\n📈 Batch Summary: {successful}/{len(tasks)} tasks completed successfully")

async def main():
    """Main function to run examples"""
    print("🤖 Gemini AI Python Examples")
    print("=" * 50)
    
    examples = {
        "1": ("Q&A Examples", example_qa),
        "2": ("Code Generation", example_code_generation),
        "3": ("Creative Writing", example_creative_writing),
        "4": ("Data Analysis", example_data_analysis),
        "5": ("Batch Processing", example_batch_processing),
        "6": ("Interactive Chat", lambda: GeminiChatBot().chat())
    }
    
    print("\nAvailable examples:")
    for key, (name, _) in examples.items():
        print(f"{key}. {name}")
    print("0. Run all examples")
    
    choice = input("\nSelect an example (0-6): ").strip()
    
    if choice == "0":
        # Run all examples except interactive chat
        for key, (name, func) in examples.items():
            if key != "6":  # Skip interactive chat
                print(f"\n{'='*20} {name} {'='*20}")
                await func()
    elif choice in examples:
        name, func = examples[choice]
        print(f"\n{'='*20} {name} {'='*20}")
        await func()
    else:
        print("Invalid choice!")

if __name__ == "__main__":
    # Check configuration
    if PROJECT_ID == "your-project-id":
        print("❌ Please update PROJECT_ID in this script")
        exit(1)
    
    # Run examples
    asyncio.run(main())
