# Gemini AI Appwrite Function

This Appwrite function integrates Google's Gemini AI to provide intelligent text generation capabilities through a serverless function.

## Features

- 🤖 **Gemini AI Integration**: Uses Google's latest Gemini 2.5 Pro model
- 🔍 **Google Search Tools**: Enhanced with real-time search capabilities
- 🚀 **Serverless**: Runs on Appwrite's serverless platform
- 🔒 **Secure**: API keys managed through environment variables
- ⚡ **Fast**: Optimized for quick response times

## Setup

### Prerequisites

- Appwrite CLI installed
- Google AI Studio account with API key
- Appwrite project set up

### Installation

1. Clone this repository:
```bash
git clone <your-repo-url>
cd gemini-ai-appwrite
```

2. Install Appwrite CLI (if not already installed):
```bash
npm install -g appwrite-cli
```

3. Login to Appwrite:
```bash
appwrite login
```

4. Deploy the function:
```bash
appwrite deploy function
```

### Configuration

1. Set up your Gemini API key in the Appwrite console:
   - Go to your Appwrite project
   - Navigate to Functions → gemini-ai-chat
   - Add environment variable: `GEMINI_API_KEY` with your Google AI Studio API key

2. Get your Google AI Studio API key:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the key to your Appwrite function environment variables

## Usage

### API Endpoint

```
POST /v1/functions/gemini-ai-chat/executions
```

### Request Format

```json
{
  "prompt": "Your question or prompt here"
}
```

### Response Format

**Success Response:**
```json
{
  "response": "Generated response from Gemini AI"
}
```

**Error Response:**
```json
{
  "error": "Error message description"
}
```

### Example Usage

#### Using cURL

```bash
curl -X POST \
  https://[YOUR-APPWRITE-ENDPOINT]/v1/functions/gemini-ai-chat/executions \
  -H "Content-Type: application/json" \
  -H "X-Appwrite-Project: [YOUR-PROJECT-ID]" \
  -d '{
    "prompt": "Explain quantum computing in simple terms"
  }'
```

#### Using JavaScript

```javascript
import { Client, Functions } from 'appwrite';

const client = new Client()
    .setEndpoint('https://[YOUR-APPWRITE-ENDPOINT]')
    .setProject('[YOUR-PROJECT-ID]');

const functions = new Functions(client);

const result = await functions.createExecution(
    'gemini-ai-chat',
    JSON.stringify({
        prompt: 'What are the benefits of renewable energy?'
    })
);

console.log(JSON.parse(result.response));
```

#### Using Python

```python
from appwrite.client import Client
from appwrite.services.functions import Functions
import json

client = Client()
client.set_endpoint('https://[YOUR-APPWRITE-ENDPOINT]')
client.set_project('[YOUR-PROJECT-ID]')

functions = Functions(client)

result = functions.create_execution(
    function_id='gemini-ai-chat',
    data=json.dumps({
        'prompt': 'How does machine learning work?'
    })
)

print(json.loads(result['response']))
```

## Function Details

### Runtime
- **Language**: Python 3.12
- **Timeout**: 30 seconds
- **Memory**: Default Appwrite allocation

### Dependencies
- `google-generativeai`: Official Google Generative AI library
- `requests`: HTTP library for additional API calls

### Environment Variables
- `GEMINI_API_KEY`: Your Google AI Studio API key (required)

## Development

### Local Testing

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variable:
```bash
export GEMINI_API_KEY="your-api-key-here"
```

3. Test the function locally using Appwrite CLI:
```bash
appwrite functions createExecution --functionId=gemini-ai-chat --data='{"prompt":"Hello, world!"}'
```

### File Structure

```
.
├── src/
│   └── main.py          # Main function code
├── appwrite.json        # Appwrite configuration
├── requirements.txt     # Python dependencies
├── README.md           # This file
└── .gitignore          # Git ignore rules
```

## Error Handling

The function includes comprehensive error handling for:
- Missing API key
- Invalid JSON payload
- Missing prompt parameter
- Gemini API errors
- Network timeouts

## Security Considerations

- API keys are stored as environment variables
- Input validation prevents malicious payloads
- Error messages don't expose sensitive information
- Function has execution timeout limits

## Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY environment variable not set"**
   - Ensure you've added the API key in Appwrite console
   - Verify the variable name is exactly `GEMINI_API_KEY`

2. **"Missing 'prompt' in request payload"**
   - Ensure your request includes a `prompt` field
   - Verify JSON formatting is correct

3. **Gemini API errors**
   - Check your API key is valid
   - Verify you have sufficient quota
   - Ensure the prompt doesn't violate content policies

### Logs

Check function logs in the Appwrite console:
1. Go to Functions → gemini-ai-chat
2. Click on "Executions" tab
3. View individual execution logs for debugging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the [Appwrite Documentation](https://appwrite.io/docs)
- Visit [Google AI Studio Documentation](https://ai.google.dev/docs)
- Open an issue in this repository
